import axios from 'axios';
import { logger } from '@/utils/logger';

export interface SMSConfig {
  username: string;
  apiKey: string;
  senderId: string;
}

export interface SMSMessage {
  to: string;
  message: string;
}

export interface SMSResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  cost?: string;
  status?: string;
}

export interface BulkSMSMessage {
  to: string[];
  message: string;
}

export interface BulkSMSResponse {
  success: boolean;
  results: Array<{
    phoneNumber: string;
    messageId?: string;
    status: string;
    cost?: string;
  }>;
  totalSent: number;
  totalFailed: number;
}

export interface DeliveryReport {
  messageId: string;
  phoneNumber: string;
  status: 'Sent' | 'Submitted' | 'Buffered' | 'Rejected' | 'Success' | 'Failed';
  failureReason?: string;
  cost?: string;
  sentAt?: Date;
  deliveredAt?: Date;
}

export class SMSService {
  private config: SMSConfig;
  private baseUrl = 'https://api.africastalking.com/version1';

  constructor() {
    this.config = {
      username: process.env['AFRICAS_TALKING_USERNAME'] || '',
      apiKey: process.env['AFRICAS_TALKING_API_KEY'] || '',
      senderId: process.env['AFRICAS_TALKING_SENDER_ID'] || 'PEDI',
    };
  }

  async sendSMS(smsData: SMSMessage): Promise<SMSResponse> {
    try {
      if (!this.config.username || !this.config.apiKey) {
        logger.warn('Africa\'s Talking credentials not configured, SMS not sent');
        return { success: false, error: 'SMS service not configured' };
      }

      const requestBody = new URLSearchParams({
        username: this.config.username,
        to: smsData.to,
        message: smsData.message,
        from: this.config.senderId,
      });

      const response = await axios.post(
        `${this.baseUrl}/messaging`,
        requestBody,
        {
          headers: {
            'apiKey': this.config.apiKey,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
          },
        }
      );

      const result = response.data.SMSMessageData;

      if (result.Recipients && result.Recipients.length > 0) {
        const recipient = result.Recipients[0];

        if (recipient.statusCode === 101) {
          logger.info('SMS sent successfully', {
            to: smsData.to,
            messageId: recipient.messageId,
            cost: recipient.cost,
          });

          return {
            success: true,
            messageId: recipient.messageId,
            cost: recipient.cost,
            status: recipient.status,
          };
        } else {
          logger.error('SMS failed to send', {
            to: smsData.to,
            statusCode: recipient.statusCode,
            status: recipient.status,
          });

          return {
            success: false,
            error: recipient.status,
            status: recipient.status,
          };
        }
      }

      return {
        success: false,
        error: 'Unknown error occurred',
      };

    } catch (error) {
      logger.error('Failed to send SMS:', error);
      return {
        success: false,
        error: 'Failed to send SMS',
      };
    }
  }

  async sendOTP(phoneNumber: string, otp: string): Promise<SMSResponse> {
    const message = `Your Pedi verification code is: ${otp}. This code will expire in 10 minutes. Do not share this code with anyone.`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  async sendTransactionNotification(phoneNumber: string, transactionType: string, details: string): Promise<SMSResponse> {
    const message = `Pedi: Your ${transactionType} transaction has been updated. ${details}. Check the app for more details.`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  async sendWelcomeMessage(phoneNumber: string, firstName: string): Promise<SMSResponse> {
    const message = `Welcome to Pedi, ${firstName}! 🌍 Start your sustainable fashion journey by listing your first item or browsing available clothes. You've earned 100 Pedi tokens to get started!`;
    
    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  async sendTokenEarnedNotification(phoneNumber: string, amount: number, reason: string): Promise<SMSResponse> {
    const message = `Pedi: You've earned ${amount} tokens for ${reason}! Your sustainable actions are making a difference. 🌱`;

    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  /**
   * Send bulk SMS to multiple recipients
   */
  async sendBulkSMS(bulkData: BulkSMSMessage): Promise<BulkSMSResponse> {
    try {
      if (!this.config.username || !this.config.apiKey) {
        logger.warn('Africa\'s Talking credentials not configured, bulk SMS not sent');
        return {
          success: false,
          results: [],
          totalSent: 0,
          totalFailed: bulkData.to.length
        };
      }

      const requestBody = new URLSearchParams({
        username: this.config.username,
        to: bulkData.to.join(','),
        message: bulkData.message,
        from: this.config.senderId,
      });

      const response = await axios.post(
        `${this.baseUrl}/messaging`,
        requestBody,
        {
          headers: {
            'apiKey': this.config.apiKey,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
          },
        }
      );

      const result = response.data.SMSMessageData;
      const results: BulkSMSResponse['results'] = [];
      let totalSent = 0;
      let totalFailed = 0;

      if (result.Recipients && result.Recipients.length > 0) {
        for (const recipient of result.Recipients) {
          const isSuccess = recipient.statusCode === 101;

          results.push({
            phoneNumber: recipient.number,
            messageId: recipient.messageId,
            status: recipient.status,
            cost: recipient.cost,
          });

          if (isSuccess) {
            totalSent++;
          } else {
            totalFailed++;
          }
        }
      }

      logger.info('Bulk SMS sent', {
        totalRecipients: bulkData.to.length,
        totalSent,
        totalFailed,
      });

      return {
        success: totalSent > 0,
        results,
        totalSent,
        totalFailed,
      };

    } catch (error) {
      logger.error('Failed to send bulk SMS:', error);
      return {
        success: false,
        results: [],
        totalSent: 0,
        totalFailed: bulkData.to.length,
      };
    }
  }

  /**
   * Get delivery reports for sent messages
   */
  async getDeliveryReports(messageIds: string[]): Promise<DeliveryReport[]> {
    try {
      if (!this.config.username || !this.config.apiKey) {
        logger.warn('Africa\'s Talking credentials not configured');
        return [];
      }

      const requestBody = new URLSearchParams({
        username: this.config.username,
        messageId: messageIds.join(','),
      });

      const response = await axios.post(
        `${this.baseUrl}/messaging/delivery/reports`,
        requestBody,
        {
          headers: {
            'apiKey': this.config.apiKey,
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
          },
        }
      );

      const reports: DeliveryReport[] = [];
      const result = response.data.SMSMessageData;

      if (result.DeliveryReports && result.DeliveryReports.length > 0) {
        for (const report of result.DeliveryReports) {
          reports.push({
            messageId: report.id,
            phoneNumber: report.phoneNumber,
            status: report.status,
            failureReason: report.failureReason,
            cost: report.cost,
            sentAt: report.sentAt ? new Date(report.sentAt) : undefined,
            deliveredAt: report.deliveredAt ? new Date(report.deliveredAt) : undefined,
          });
        }
      }

      return reports;

    } catch (error) {
      logger.error('Failed to get delivery reports:', error);
      return [];
    }
  }

  /**
   * Send payment confirmation notification
   */
  async sendPaymentConfirmation(phoneNumber: string, amount: number, paymentId: string, type: string): Promise<SMSResponse> {
    const message = `Pedi: Your ${type} payment of KES ${amount.toLocaleString()} has been confirmed. Payment ID: ${paymentId}. Thank you for using Pedi! 💚`;

    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  /**
   * Send exchange notification
   */
  async sendExchangeNotification(phoneNumber: string, type: 'request' | 'accepted' | 'completed' | 'cancelled', itemName: string, partnerName: string): Promise<SMSResponse> {
    let message = '';

    switch (type) {
      case 'request':
        message = `Pedi: ${partnerName} wants to exchange "${itemName}" with you. Check the app to respond.`;
        break;
      case 'accepted':
        message = `Pedi: Your exchange request for "${itemName}" has been accepted by ${partnerName}. Arrange pickup/delivery in the app.`;
        break;
      case 'completed':
        message = `Pedi: Your exchange of "${itemName}" with ${partnerName} is complete! You've earned tokens for sustainable fashion. 🌍`;
        break;
      case 'cancelled':
        message = `Pedi: Your exchange of "${itemName}" with ${partnerName} has been cancelled. Browse other items in the app.`;
        break;
    }

    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  /**
   * Send reminder notification
   */
  async sendReminder(phoneNumber: string, type: 'pickup' | 'delivery' | 'payment' | 'verification', details: string): Promise<SMSResponse> {
    const message = `Pedi Reminder: ${details}. Don't forget to complete your ${type} to keep your sustainable fashion journey going! 📱`;

    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  /**
   * Send promotional notification
   */
  async sendPromotion(phoneNumber: string, title: string, description: string, actionText?: string): Promise<SMSResponse> {
    let message = `Pedi: ${title} - ${description}`;
    if (actionText) {
      message += ` ${actionText}`;
    }
    message += ' Open the app to learn more! 🛍️';

    return this.sendSMS({
      to: phoneNumber,
      message,
    });
  }

  /**
   * Send system alert
   */
  async sendSystemAlert(phoneNumber: string, alertType: 'maintenance' | 'security' | 'update', message: string): Promise<SMSResponse> {
    const prefix = alertType === 'maintenance' ? '🔧' : alertType === 'security' ? '🔒' : '📱';
    const fullMessage = `Pedi Alert ${prefix}: ${message}`;

    return this.sendSMS({
      to: phoneNumber,
      message: fullMessage,
    });
  }

  /**
   * Validate phone number format
   */
  validatePhoneNumber(phoneNumber: string): boolean {
    return /^(\+254|254|0)[17]\d{8}$/.test(phoneNumber);
  }

  /**
   * Format phone number to international format
   */
  formatPhoneNumber(phoneNumber: string): string {
    // Remove any spaces, dashes, or parentheses
    let cleaned = phoneNumber.replace(/[\s\-\(\)]/g, '');

    // Handle different formats
    if (cleaned.startsWith('0')) {
      cleaned = '+254' + cleaned.substring(1);
    } else if (cleaned.startsWith('254')) {
      cleaned = '+' + cleaned;
    } else if (!cleaned.startsWith('+254')) {
      cleaned = '+254' + cleaned;
    }

    return cleaned;
  }

  /**
   * Get SMS service statistics
   */
  getServiceStats(): {
    isConfigured: boolean;
    username: string;
    senderId: string;
  } {
    return {
      isConfigured: !!(this.config.username && this.config.apiKey),
      username: this.config.username,
      senderId: this.config.senderId,
    };
  }
}

export const smsService = new SMSService();
