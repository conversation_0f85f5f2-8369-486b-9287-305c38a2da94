import User from '@/models/User';
import TokenTransaction from '@/models/TokenTransaction';
import { logger } from '@/utils/logger';
import { calculateTokenReward } from '@/utils/helpers';
import { smsService } from './sms';

export interface TokenEarnRequest {
  userId: string;
  action: string;
  amount?: number;
  referenceId?: string;
  description: string;
  metadata?: Record<string, any>;
}

export interface TokenSpendRequest {
  userId: string;
  amount: number;
  referenceId?: string;
  description: string;
  metadata?: Record<string, any>;
}

export class TokenService {
  async earnTokens(request: TokenEarnRequest): Promise<{ success: boolean; newBalance: number; error?: string }> {
    try {
      const user = await User.findById(request.userId);
      if (!user) {
        return { success: false, newBalance: 0, error: 'User not found' };
      }

      // Calculate token amount if not provided
      const amount = request.amount || calculateTokenReward(request.action, request.metadata);
      
      if (amount <= 0) {
        return { success: false, newBalance: user.pediTokens, error: 'Invalid token amount' };
      }

      // Update user balance
      user.pediTokens += amount;
      const newBalance = user.pediTokens;
      await user.save();

      // Create token transaction record
      const tokenTransaction = new TokenTransaction({
        user: request.userId,
        type: 'earn',
        amount,
        balance: newBalance,
        source: {
          type: this.mapActionToSourceType(request.action),
          referenceId: request.referenceId,
          description: request.description,
        },
        metadata: request.metadata,
      });

      await tokenTransaction.save();

      // Send notification for significant token earnings
      if (amount >= 25) {
        await smsService.sendTokenEarnedNotification(
          user.phoneNumber,
          amount,
          request.description
        );

        // Also send through notification service for better tracking
        try {
          const { notificationIntegrationService } = await import('./notificationIntegration');
          await notificationIntegrationService.sendTokenEarned(
            request.userId,
            amount,
            request.description,
            request.referenceId
          );
        } catch (notificationError) {
          logger.error('Failed to send token earned notification:', notificationError);
        }
      }

      logger.info('Tokens earned successfully', {
        userId: request.userId,
        amount,
        newBalance,
        action: request.action,
      });

      return { success: true, newBalance };

    } catch (error) {
      logger.error('Error earning tokens:', error);
      return { success: false, newBalance: 0, error: 'Failed to earn tokens' };
    }
  }

  async spendTokens(request: TokenSpendRequest): Promise<{ success: boolean; newBalance: number; error?: string }> {
    try {
      const user = await User.findById(request.userId);
      if (!user) {
        return { success: false, newBalance: 0, error: 'User not found' };
      }

      if (request.amount <= 0) {
        return { success: false, newBalance: user.pediTokens, error: 'Invalid token amount' };
      }

      if (user.pediTokens < request.amount) {
        return { success: false, newBalance: user.pediTokens, error: 'Insufficient token balance' };
      }

      // Update user balance
      user.pediTokens -= request.amount;
      const newBalance = user.pediTokens;
      await user.save();

      // Create token transaction record
      const tokenTransaction = new TokenTransaction({
        user: request.userId,
        type: 'spend',
        amount: -request.amount, // Negative for spending
        balance: newBalance,
        source: {
          type: 'purchase',
          referenceId: request.referenceId,
          description: request.description,
        },
        metadata: request.metadata,
      });

      await tokenTransaction.save();

      logger.info('Tokens spent successfully', {
        userId: request.userId,
        amount: request.amount,
        newBalance,
      });

      return { success: true, newBalance };

    } catch (error) {
      logger.error('Error spending tokens:', error);
      return { success: false, newBalance: 0, error: 'Failed to spend tokens' };
    }
  }

  async refundTokens(request: TokenSpendRequest): Promise<{ success: boolean; newBalance: number; error?: string }> {
    try {
      const user = await User.findById(request.userId);
      if (!user) {
        return { success: false, newBalance: 0, error: 'User not found' };
      }

      if (request.amount <= 0) {
        return { success: false, newBalance: user.pediTokens, error: 'Invalid token amount' };
      }

      // Update user balance
      user.pediTokens += request.amount;
      const newBalance = user.pediTokens;
      await user.save();

      // Create token transaction record
      const tokenTransaction = new TokenTransaction({
        user: request.userId,
        type: 'refund',
        amount: request.amount,
        balance: newBalance,
        source: {
          type: 'admin',
          referenceId: request.referenceId,
          description: request.description,
        },
        metadata: request.metadata,
      });

      await tokenTransaction.save();

      logger.info('Tokens refunded successfully', {
        userId: request.userId,
        amount: request.amount,
        newBalance,
      });

      return { success: true, newBalance };

    } catch (error) {
      logger.error('Error refunding tokens:', error);
      return { success: false, newBalance: 0, error: 'Failed to refund tokens' };
    }
  }

  async getTokenBalance(userId: string): Promise<{ success: boolean; balance: number; error?: string }> {
    try {
      const user = await User.findById(userId).select('pediTokens');
      if (!user) {
        return { success: false, balance: 0, error: 'User not found' };
      }

      return { success: true, balance: user.pediTokens };

    } catch (error) {
      logger.error('Error getting token balance:', error);
      return { success: false, balance: 0, error: 'Failed to get token balance' };
    }
  }

  async getTokenHistory(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      type?: 'earn' | 'spend' | 'bonus' | 'refund' | 'penalty';
      source?: 'listing' | 'donation' | 'swap' | 'purchase' | 'referral' | 'welcome' | 'daily_login' | 'review' | 'admin';
    } = {}
  ): Promise<{
    success: boolean;
    transactions: any[];
    pagination?: any;
    error?: string;
  }> {
    try {
      const page = options.page || 1;
      const limit = options.limit || 20;
      const skip = (page - 1) * limit;

      const query: any = { user: userId };
      if (options.type) {
        query.type = options.type;
      }
      if (options.source) {
        query['source.type'] = options.source;
      }

      const [transactions, total] = await Promise.all([
        TokenTransaction.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate('user', 'firstName lastName phoneNumber'),
        TokenTransaction.countDocuments(query),
      ]);

      const pagination = {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      };

      return {
        success: true,
        transactions,
        pagination,
      };

    } catch (error) {
      logger.error('Error getting token history:', error);
      return {
        success: false,
        transactions: [],
        error: 'Failed to get token history',
      };
    }
  }

  private mapActionToSourceType(action: string): string {
    const mapping: Record<string, string> = {
      'listing': 'listing',
      'donation': 'donation',
      'swap_completed': 'swap',
      'review_given': 'review',
      'profile_completed': 'admin',
      'referral': 'referral',
      'daily_login': 'daily_login',
      'first_listing': 'listing',
      'first_swap': 'swap',
      'first_donation': 'donation',
    };

    return mapping[action] || 'admin';
  }

  // Daily login bonus
  async processDailyLoginBonus(userId: string): Promise<{ success: boolean; tokensEarned: number }> {
    try {
      // Check if user already got daily bonus today
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const existingBonus = await TokenTransaction.findOne({
        user: userId,
        'source.type': 'daily_login',
        createdAt: { $gte: today },
      });

      if (existingBonus) {
        return { success: false, tokensEarned: 0 };
      }

      const result = await this.earnTokens({
        userId,
        action: 'daily_login',
        description: 'Daily login bonus',
      });

      return {
        success: result.success,
        tokensEarned: result.success ? calculateTokenReward('daily_login') : 0,
      };

    } catch (error) {
      logger.error('Error processing daily login bonus:', error);
      return { success: false, tokensEarned: 0 };
    }
  }

  // Get token leaderboard
  async getTokenLeaderboard(limit: number = 10): Promise<any[]> {
    try {
      const leaderboard = await User.find({ isActive: true })
        .select('firstName lastName pediTokens sustainabilityScore totalDonations totalSwaps location.county')
        .sort({ pediTokens: -1 })
        .limit(limit);

      return leaderboard.map((user, index) => ({
        rank: index + 1,
        userId: user._id,
        name: `${user.firstName} ${user.lastName}`,
        tokens: user.pediTokens,
        sustainabilityScore: user.sustainabilityScore,
        totalDonations: user.totalDonations,
        totalSwaps: user.totalSwaps,
        county: user.location?.county || 'Unknown'
      }));

    } catch (error) {
      logger.error('Error getting token leaderboard:', error);
      return [];
    }
  }

  // Get token statistics for a user
  async getTokenStats(userId: string): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Get transaction statistics
      const [earnStats, spendStats, totalTransactions] = await Promise.all([
        TokenTransaction.aggregate([
          { $match: { user: user._id, type: 'earn' } },
          {
            $group: {
              _id: '$source.type',
              totalAmount: { $sum: '$amount' },
              count: { $sum: 1 }
            }
          }
        ]),
        TokenTransaction.aggregate([
          { $match: { user: user._id, type: 'spend' } },
          {
            $group: {
              _id: '$source.type',
              totalAmount: { $sum: { $abs: '$amount' } },
              count: { $sum: 1 }
            }
          }
        ]),
        TokenTransaction.countDocuments({ user: userId })
      ]);

      // Calculate total earned and spent
      const totalEarned = earnStats.reduce((sum, stat) => sum + stat.totalAmount, 0);
      const totalSpent = spendStats.reduce((sum, stat) => sum + stat.totalAmount, 0);

      // Get recent activity (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentActivity = await TokenTransaction.find({
        user: userId,
        createdAt: { $gte: thirtyDaysAgo }
      }).countDocuments();

      return {
        success: true,
        data: {
          currentBalance: user.pediTokens,
          totalEarned,
          totalSpent,
          totalTransactions,
          recentActivity,
          earnBreakdown: earnStats,
          spendBreakdown: spendStats,
          joinedAt: user.joinedAt,
          sustainabilityScore: user.sustainabilityScore
        }
      };

    } catch (error) {
      logger.error('Error getting token stats:', error);
      return { success: false, error: 'Failed to get token statistics' };
    }
  }

  // Get earning opportunities for a user
  async getEarningOpportunities(userId: string): Promise<any[]> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        return [];
      }

      const opportunities = [];

      // Check if daily bonus is available
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayBonus = await TokenTransaction.findOne({
        user: userId,
        'source.type': 'daily_login',
        createdAt: { $gte: today }
      });

      if (!todayBonus) {
        opportunities.push({
          type: 'daily_login',
          title: 'Daily Login Bonus',
          description: 'Claim your daily 5 tokens for logging in',
          tokens: 5,
          action: 'claim_daily_bonus',
          available: true
        });
      }

      // Check for listing opportunities
      const userListings = await TokenTransaction.countDocuments({
        user: userId,
        'source.type': 'listing'
      });

      if (userListings < 5) {
        opportunities.push({
          type: 'listing',
          title: 'List More Items',
          description: 'Earn 10 tokens for each clothing item you list',
          tokens: 10,
          action: 'list_item',
          available: true
        });
      }

      // Check for review opportunities
      const userReviews = await TokenTransaction.countDocuments({
        user: userId,
        'source.type': 'review'
      });

      if (userReviews < 10) {
        opportunities.push({
          type: 'review',
          title: 'Write Reviews',
          description: 'Earn 3 tokens for each transaction review',
          tokens: 3,
          action: 'write_review',
          available: true
        });
      }

      // Referral opportunity
      opportunities.push({
        type: 'referral',
        title: 'Refer Friends',
        description: 'Earn 50 tokens for each friend who joins Pedi',
        tokens: 50,
        action: 'refer_friend',
        available: true
      });

      return opportunities;

    } catch (error) {
      logger.error('Error getting earning opportunities:', error);
      return [];
    }
  }

  // Transfer tokens between users
  async transferTokens(request: {
    senderId: string;
    recipientId: string;
    amount: number;
    message: string;
  }): Promise<{
    success: boolean;
    senderNewBalance?: number;
    recipientNewBalance?: number;
    error?: string;
  }> {
    try {
      const [sender, recipient] = await Promise.all([
        User.findById(request.senderId),
        User.findById(request.recipientId)
      ]);

      if (!sender || !recipient) {
        return { success: false, error: 'User not found' };
      }

      if (sender.pediTokens < request.amount) {
        return { success: false, error: 'Insufficient token balance' };
      }

      // Update balances
      sender.pediTokens -= request.amount;
      recipient.pediTokens += request.amount;

      await Promise.all([sender.save(), recipient.save()]);

      // Create transaction records
      const senderTransaction = new TokenTransaction({
        user: request.senderId,
        type: 'spend',
        amount: -request.amount,
        balance: sender.pediTokens,
        source: {
          type: 'admin', // Using admin type for transfers
          referenceId: request.recipientId,
          description: `Transfer to ${recipient.firstName} ${recipient.lastName}: ${request.message}`,
        },
        metadata: {
          transferType: 'outgoing',
          recipientId: request.recipientId,
          recipientName: `${recipient.firstName} ${recipient.lastName}`
        }
      });

      const recipientTransaction = new TokenTransaction({
        user: request.recipientId,
        type: 'earn',
        amount: request.amount,
        balance: recipient.pediTokens,
        source: {
          type: 'admin', // Using admin type for transfers
          referenceId: request.senderId,
          description: `Transfer from ${sender.firstName} ${sender.lastName}: ${request.message}`,
        },
        metadata: {
          transferType: 'incoming',
          senderId: request.senderId,
          senderName: `${sender.firstName} ${sender.lastName}`
        }
      });

      await Promise.all([senderTransaction.save(), recipientTransaction.save()]);

      logger.info('Tokens transferred successfully', {
        senderId: request.senderId,
        recipientId: request.recipientId,
        amount: request.amount
      });

      return {
        success: true,
        senderNewBalance: sender.pediTokens,
        recipientNewBalance: recipient.pediTokens
      };

    } catch (error) {
      logger.error('Error transferring tokens:', error);
      return { success: false, error: 'Failed to transfer tokens' };
    }
  }
}

export const tokenService = new TokenService();
