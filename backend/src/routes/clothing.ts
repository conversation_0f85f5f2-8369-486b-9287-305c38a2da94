import express, { Request, Response, NextFunction } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import multer from 'multer';
import ClothingItem from '../models/ClothingItem';
import User from '../models/User';
import { authenticate } from '../middleware/auth';
import { createError } from '../middleware/errorHandler';
import { cloudinaryService } from '../services/cloudinary';
import { tokenService } from '../services/token';
import { processListingRewards } from '../middleware/tokenRewards';
import logger from '../utils/logger';

const router = express.Router();

// Configure multer for image uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 5, // Maximum 5 images
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

// Validation middleware
const validateClothingItem = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Title must be between 3 and 100 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  body('category')
    .isIn(['Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories', 'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage', 'Underwear'])
    .withMessage('Invalid category'),
  body('size')
    .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'])
    .withMessage('Invalid size'),
  body('color')
    .trim()
    .isLength({ min: 2, max: 30 })
    .withMessage('Color must be between 2 and 30 characters'),
  body('condition')
    .isIn(['New', 'Like New', 'Good', 'Fair', 'Poor'])
    .withMessage('Invalid condition'),
  body('exchangeType')
    .isIn(['swap', 'token', 'donation', 'all'])
    .withMessage('Invalid exchange type'),
  body('sustainabilityInfo.material')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Material must be between 2 and 100 characters'),
  body('sustainabilityInfo.careInstructions')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Care instructions must be between 5 and 200 characters'),
  body('sustainabilityInfo.estimatedLifespan')
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Estimated lifespan must be between 3 and 50 characters'),
];

const validateSearch = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
  query('category')
    .optional()
    .isIn(['Tops', 'Bottoms', 'Dresses', 'Outerwear', 'Shoes', 'Accessories', 'Formal', 'Casual', 'Sportswear', 'Traditional', 'Vintage', 'Underwear'])
    .withMessage('Invalid category'),
  query('size')
    .optional()
    .isIn(['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', 'One Size', 'Custom'])
    .withMessage('Invalid size'),
  query('condition')
    .optional()
    .isIn(['New', 'Like New', 'Good', 'Fair', 'Poor'])
    .withMessage('Invalid condition'),
  query('exchangeType')
    .optional()
    .isIn(['swap', 'token', 'donation', 'all'])
    .withMessage('Invalid exchange type'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum price must be a positive number'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Maximum price must be a positive number'),
];

// GET /api/clothing - Get all clothing items with filtering and pagination
router.get('/', validateSearch, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const {
      page = 1,
      limit = 20,
      category,
      size,
      condition,
      exchangeType,
      county,
      town,
      search,
      minPrice,
      maxPrice,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      owner,
    } = req.query;

    // Build filter object
    const filter: any = { isAvailable: true };

    if (category) filter.category = category;
    if (size) filter.size = size;
    if (condition) filter.condition = condition;
    if (exchangeType && exchangeType !== 'all') filter.exchangeType = { $in: [exchangeType, 'all'] };
    if (county) filter['location.county'] = county;
    if (town) filter['location.town'] = new RegExp(town as string, 'i');
    if (owner) filter.owner = owner;

    // Price filtering for token purchases
    if (minPrice || maxPrice) {
      filter.tokenPrice = {};
      if (minPrice) filter.tokenPrice.$gte = parseFloat(minPrice as string);
      if (maxPrice) filter.tokenPrice.$lte = parseFloat(maxPrice as string);
    }

    // Text search
    if (search) {
      filter.$text = { $search: search as string };
    }

    // Build sort object
    const sort: any = {};
    if (search) {
      sort.score = { $meta: 'textScore' };
    }
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const skip = (parseInt(page as string) - 1) * parseInt(limit as string);

    const [items, total] = await Promise.all([
      ClothingItem.find(filter)
        .populate('owner', 'firstName lastName profilePicture rating sustainabilityScore')
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit as string))
        .lean(),
      ClothingItem.countDocuments(filter),
    ]);

    res.json({
      success: true,
      data: {
        items,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total,
          pages: Math.ceil(total / parseInt(limit as string)),
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching clothing items:', error);
    next(createError('Failed to fetch clothing items', 500));
  }
});

// GET /api/clothing/:id - Get single clothing item
router.get('/:id', param('id').isMongoId().withMessage('Invalid item ID'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const item = await ClothingItem.findById(req.params.id)
      .populate('owner', 'firstName lastName profilePicture rating sustainabilityScore location')
      .populate('likes', 'firstName lastName profilePicture');

    if (!item) {
      return next(createError('Clothing item not found', 404));
    }

    // Increment view count (but not for the owner)
    const userId = (req as any).user?.id;
    if (userId && item.owner._id.toString() !== userId) {
      await ClothingItem.findByIdAndUpdate(req.params.id, { $inc: { views: 1 } });
      item.views += 1;
    }

    res.json({
      success: true,
      data: item,
    });
  } catch (error) {
    logger.error('Error fetching clothing item:', error);
    next(createError('Failed to fetch clothing item', 500));
  }
});

// POST /api/clothing - Create new clothing item
router.post('/', authenticate, upload.array('images', 5), validateClothingItem, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const userId = (req as any).user.id;
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return next(createError('At least one image is required', 400));
    }

    // Upload images to Cloudinary
    const imageUrls: string[] = [];
    try {
      const buffers = files.map(file => file.buffer);
      const uploadResults = await cloudinaryService.uploadMultipleImages(buffers, {
        folder: 'pedi/clothing',
        transformation: [
          { width: 800, height: 800, crop: 'limit', quality: 'auto' },
          { format: 'auto' },
        ],
      });

      for (const result of uploadResults) {
        if (result.success && result.url) {
          imageUrls.push(result.url);
        } else {
          throw new Error(result.error || 'Failed to upload image');
        }
      }
    } catch (uploadError) {
      logger.error('Error uploading images:', uploadError);
      return next(createError('Failed to upload images', 500));
    }

    // Get user location
    const user = await User.findById(userId);
    if (!user) {
      return next(createError('User not found', 404));
    }

    // Create clothing item
    const clothingData = {
      ...req.body,
      owner: userId,
      images: imageUrls,
      location: user.location,
      tags: req.body.tags ? req.body.tags.split(',').map((tag: string) => tag.trim().toLowerCase()) : [],
    };

    const clothingItem = new ClothingItem(clothingData);
    await clothingItem.save();

    // Token rewards are handled by processListingRewards middleware

    // Populate owner info for response
    await clothingItem.populate('owner', 'firstName lastName profilePicture rating sustainabilityScore');

    res.status(201).json({
      success: true,
      message: 'Clothing item listed successfully! You earned Pedi tokens.',
      data: clothingItem
    });
  } catch (error) {
    logger.error('Error creating clothing item:', error);
    next(createError('Failed to create clothing item', 500));
  }
});

// PUT /api/clothing/:id - Update clothing item (owner only)
router.put('/:id', authenticate, upload.array('images', 5), param('id').isMongoId().withMessage('Invalid item ID'), validateClothingItem, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const userId = (req as any).user.id;
    const itemId = req.params.id;

    const existingItem = await ClothingItem.findById(itemId);
    if (!existingItem) {
      return next(createError('Clothing item not found', 404));
    }

    if (existingItem.owner.toString() !== userId) {
      return next(createError('You can only update your own items', 403));
    }

    // Handle new image uploads
    const files = req.files as Express.Multer.File[];
    let imageUrls = existingItem.images;

    if (files && files.length > 0) {
      // Extract public IDs from old image URLs and delete them
      const oldPublicIds = existingItem.images.map(url => cloudinaryService.extractPublicId(url));

      await cloudinaryService.deleteMultipleImages(oldPublicIds);

      // Upload new images
      const buffers = files.map(file => file.buffer);
      const uploadResults = await cloudinaryService.uploadMultipleImages(buffers, {
        folder: 'pedi/clothing',
        transformation: [
          { width: 800, height: 800, crop: 'limit', quality: 'auto' },
          { format: 'auto' },
        ],
      });

      imageUrls = [];
      for (const result of uploadResults) {
        if (result.success && result.url) {
          imageUrls.push(result.url);
        } else {
          throw new Error(result.error || 'Failed to upload image');
        }
      }
    }

    // Update clothing item
    const updateData = {
      ...req.body,
      images: imageUrls,
      tags: req.body.tags ? req.body.tags.split(',').map((tag: string) => tag.trim().toLowerCase()) : existingItem.tags,
    };

    const updatedItem = await ClothingItem.findByIdAndUpdate(
      itemId,
      updateData,
      { new: true, runValidators: true }
    ).populate('owner', 'firstName lastName profilePicture rating sustainabilityScore');

    res.json({
      success: true,
      message: 'Clothing item updated successfully',
      data: updatedItem,
    });
  } catch (error) {
    logger.error('Error updating clothing item:', error);
    next(createError('Failed to update clothing item', 500));
  }
});

// DELETE /api/clothing/:id - Delete clothing item (owner only)
router.delete('/:id', authenticate, param('id').isMongoId().withMessage('Invalid item ID'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const userId = (req as any).user.id;
    const itemId = req.params.id;

    const item = await ClothingItem.findById(itemId);
    if (!item) {
      return next(createError('Clothing item not found', 404));
    }

    if (item.owner.toString() !== userId) {
      return next(createError('You can only delete your own items', 403));
    }

    // Delete images from Cloudinary
    const publicIds = item.images.map(url => cloudinaryService.extractPublicId(url));

    await cloudinaryService.deleteMultipleImages(publicIds);

    await ClothingItem.findByIdAndDelete(itemId);

    res.json({
      success: true,
      message: 'Clothing item deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting clothing item:', error);
    next(createError('Failed to delete clothing item', 500));
  }
});

// POST /api/clothing/:id/like - Like/unlike clothing item
router.post('/:id/like', authenticate, param('id').isMongoId().withMessage('Invalid item ID'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(createError('Validation failed', 400, errors.array()));
    }

    const userId = (req as any).user.id;
    const itemId = req.params.id;

    const item = await ClothingItem.findById(itemId);
    if (!item) {
      return next(createError('Clothing item not found', 404));
    }

    const isLiked = item.likes.includes(userId);

    if (isLiked) {
      // Unlike
      item.likes = item.likes.filter(id => id.toString() !== userId);
    } else {
      // Like
      item.likes.push(userId);
    }

    await item.save();

    res.json({
      success: true,
      message: isLiked ? 'Item unliked' : 'Item liked',
      data: {
        isLiked: !isLiked,
        likeCount: item.likes.length,
      },
    });
  } catch (error) {
    logger.error('Error toggling like:', error);
    next(createError('Failed to toggle like', 500));
  }
});

export default router;
