'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import { Recycle, Leaf, Users, Heart } from 'lucide-react';

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState('login');

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl grid lg:grid-cols-2 gap-8 items-center">
        {/* Left side - Branding */}
        <div className="hidden lg:block space-y-8">
          <div className="text-center lg:text-left">
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-4">
              <span className="text-green-600">Pedi</span>
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 mb-8">
              The Future of Fashion is Circular
            </p>
            <p className="text-lg text-gray-500 leading-relaxed">
              Join Kenya's first sustainable clothing exchange platform. 
              Swap, donate, and discover pre-loved fashion while earning Pedi tokens 
              and making a positive impact on our environment.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div className="text-center p-4 bg-white/50 rounded-lg">
              <Recycle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Circular Fashion</h3>
              <p className="text-sm text-gray-600">Reduce textile waste through clothing swaps</p>
            </div>
            
            <div className="text-center p-4 bg-white/50 rounded-lg">
              <Leaf className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Eco-Friendly</h3>
              <p className="text-sm text-gray-600">Lower your carbon footprint</p>
            </div>
            
            <div className="text-center p-4 bg-white/50 rounded-lg">
              <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Community</h3>
              <p className="text-sm text-gray-600">Connect with like-minded fashion lovers</p>
            </div>
            
            <div className="text-center p-4 bg-white/50 rounded-lg">
              <Heart className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Give Back</h3>
              <p className="text-sm text-gray-600">Donate to local charities</p>
            </div>
          </div>

          <div className="bg-white/70 rounded-lg p-6">
            <h3 className="font-semibold text-gray-900 mb-3">How it works:</h3>
            <ol className="space-y-2 text-sm text-gray-600">
              <li className="flex items-start gap-2">
                <span className="bg-green-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">1</span>
                List your pre-loved clothes with photos and descriptions
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-green-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">2</span>
                Browse and discover amazing pieces from other users
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-green-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">3</span>
                Swap using Pedi tokens or arrange direct exchanges
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-green-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">4</span>
                Earn tokens for sustainable actions and community participation
              </li>
            </ol>
          </div>
        </div>

        {/* Right side - Auth Forms */}
        <div className="w-full max-w-md mx-auto lg:mx-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="login">Sign In</TabsTrigger>
              <TabsTrigger value="register">Sign Up</TabsTrigger>
            </TabsList>
            
            <TabsContent value="login">
              <LoginForm onSwitchToRegister={() => setActiveTab('register')} />
            </TabsContent>
            
            <TabsContent value="register">
              <RegisterForm onSwitchToLogin={() => setActiveTab('login')} />
            </TabsContent>
          </Tabs>

          {/* Mobile branding */}
          <div className="lg:hidden mt-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Welcome to <span className="text-green-600">Pedi</span>
            </h2>
            <p className="text-gray-600">
              Kenya's sustainable fashion exchange platform
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
