import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle, 
  RefreshCw,
  Receipt,
  Smartphone
} from 'lucide-react';

interface Payment {
  paymentId: string;
  amount: number;
  currency: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  mpesaReceiptNumber?: string;
  mpesaDetails?: {
    phoneNumber: string;
    transactionDate?: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface PaymentStatusProps {
  paymentId: string;
  onStatusChange?: (status: string) => void;
  showRefreshButton?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

export const PaymentStatus: React.FC<PaymentStatusProps> = ({
  paymentId,
  onStatusChange,
  showRefreshButton = true,
  autoRefresh = false,
  refreshInterval = 10000, // 10 seconds
}) => {
  const [payment, setPayment] = useState<Payment | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string>('');
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    fetchPaymentStatus();
    
    if (autoRefresh) {
      const interval = setInterval(fetchPaymentStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [paymentId, autoRefresh, refreshInterval]);

  useEffect(() => {
    if (payment && onStatusChange) {
      onStatusChange(payment.status);
    }
  }, [payment?.status, onStatusChange]);

  const fetchPaymentStatus = async (showLoader = true) => {
    try {
      if (showLoader) {
        setIsLoading(true);
      } else {
        setIsRefreshing(true);
      }
      setError('');

      const response = await fetch(`/api/payments/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment status');
      }

      const data = await response.json();
      setPayment(data.data);
      setLastUpdated(new Date());
    } catch (error: any) {
      console.error('Failed to fetch payment status:', error);
      setError(error.message);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchPaymentStatus(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'refunded':
        return <RefreshCw className="h-5 w-5 text-orange-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      pending: 'outline',
      processing: 'default',
      completed: 'default',
      failed: 'destructive',
      cancelled: 'destructive',
      refunded: 'secondary',
    };

    const colors: Record<string, string> = {
      pending: 'bg-yellow-100 text-yellow-800 border-yellow-300',
      processing: 'bg-blue-100 text-blue-800 border-blue-300',
      completed: 'bg-green-100 text-green-800 border-green-300',
      failed: 'bg-red-100 text-red-800 border-red-300',
      cancelled: 'bg-red-100 text-red-800 border-red-300',
      refunded: 'bg-orange-100 text-orange-800 border-orange-300',
    };

    return (
      <Badge className={colors[status] || 'bg-gray-100 text-gray-800'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Payment is waiting to be processed';
      case 'processing':
        return 'Please check your phone and enter your M-Pesa PIN';
      case 'completed':
        return 'Payment completed successfully';
      case 'failed':
        return 'Payment failed - please try again';
      case 'cancelled':
        return 'Payment was cancelled';
      case 'refunded':
        return 'Payment has been refunded';
      default:
        return 'Unknown payment status';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-KE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    if (phoneNumber.startsWith('254')) {
      return `+${phoneNumber.slice(0, 3)} ${phoneNumber.slice(3, 6)} ${phoneNumber.slice(6)}`;
    }
    return phoneNumber;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading payment status...
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          {showRefreshButton && (
            <Button 
              onClick={handleRefresh} 
              variant="outline" 
              className="mt-4"
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Try Again
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  if (!payment) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Payment not found</AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(payment.status)}
            Payment Status
          </CardTitle>
          {showRefreshButton && (
            <Button 
              onClick={handleRefresh} 
              variant="ghost" 
              size="sm"
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
        <CardDescription>
          Payment ID: {payment.paymentId}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Badge and Message */}
        <div className="space-y-2">
          {getStatusBadge(payment.status)}
          <p className="text-sm text-gray-600">{getStatusMessage(payment.status)}</p>
        </div>

        {/* Payment Details */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="font-medium text-gray-700">Amount</p>
            <p className="text-lg font-semibold">
              {payment.currency} {payment.amount.toLocaleString()}
            </p>
          </div>
          <div>
            <p className="font-medium text-gray-700">Description</p>
            <p>{payment.description}</p>
          </div>
        </div>

        {/* M-Pesa Details */}
        {payment.mpesaDetails && (
          <div className="space-y-2">
            <p className="font-medium text-gray-700 flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              M-Pesa Details
            </p>
            <div className="bg-gray-50 p-3 rounded-lg space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Phone Number:</span>
                <span>{formatPhoneNumber(payment.mpesaDetails.phoneNumber)}</span>
              </div>
              {payment.mpesaReceiptNumber && (
                <div className="flex justify-between">
                  <span>Receipt Number:</span>
                  <span className="font-mono">{payment.mpesaReceiptNumber}</span>
                </div>
              )}
              {payment.mpesaDetails.transactionDate && (
                <div className="flex justify-between">
                  <span>Transaction Date:</span>
                  <span>{formatDate(payment.mpesaDetails.transactionDate)}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Receipt Button */}
        {payment.status === 'completed' && payment.mpesaReceiptNumber && (
          <Button variant="outline" className="w-full">
            <Receipt className="h-4 w-4 mr-2" />
            Download Receipt
          </Button>
        )}

        {/* Timestamps */}
        <div className="text-xs text-gray-500 space-y-1">
          <div className="flex justify-between">
            <span>Created:</span>
            <span>{formatDate(payment.createdAt)}</span>
          </div>
          <div className="flex justify-between">
            <span>Last Updated:</span>
            <span>{formatDate(payment.updatedAt)}</span>
          </div>
          <div className="flex justify-between">
            <span>Status Checked:</span>
            <span>{lastUpdated.toLocaleTimeString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
