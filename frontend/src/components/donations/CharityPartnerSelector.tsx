import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building, 
  Search, 
  MapPin, 
  Star, 
  Users, 
  Package, 
  CheckCircle, 
  Clock,
  AlertTriangle,
  Heart,
  Phone,
  Mail,
  Globe,
  Loader2,
  AlertCircle,
  Filter
} from 'lucide-react';
import { ClothingItem, CharityPartner } from './DonationForm';

interface CharityPartnerSelectorProps {
  selectedCharity: CharityPartner | null;
  onCharitySelected: (charity: CharityPartner) => void;
  selectedItems: ClothingItem[];
  className?: string;
}

interface CharityFilters {
  search: string;
  category: string;
  county: string;
  acceptsAllItems: boolean;
}

export const CharityPartnerSelector: React.FC<CharityPartnerSelectorProps> = ({
  selectedCharity,
  onCharitySelected,
  selectedItems,
  className = ''
}) => {
  const [charityPartners, setCharityPartners] = useState<CharityPartner[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [filters, setFilters] = useState<CharityFilters>({
    search: '',
    category: '',
    county: '',
    acceptsAllItems: false
  });

  const CATEGORIES = [
    'Children & Youth', 'Women Empowerment', 'Education', 'Healthcare',
    'Environment', 'Community Development', 'Disaster Relief', 'Elderly Care',
    'Disability Support', 'General Welfare'
  ];

  const KENYAN_COUNTIES = [
    'Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Malindi',
    'Kitale', 'Garissa', 'Kakamega', 'Machakos', 'Meru', 'Nyeri', 'Kericho'
  ];

  useEffect(() => {
    fetchCharityPartners();
  }, [filters]);

  const fetchCharityPartners = async () => {
    setIsLoading(true);
    setError('');

    try {
      const queryParams = new URLSearchParams({
        verificationStatus: 'verified',
        isActive: 'true',
        limit: '20'
      });

      if (filters.search) queryParams.append('search', filters.search);
      if (filters.category) queryParams.append('category', filters.category);
      if (filters.county) queryParams.append('county', filters.county);

      const response = await fetch(`/api/charity-partners?${queryParams}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch charity partners');
      }

      const data = await response.json();
      let partners = data.data.charityPartners || [];

      // Filter by item compatibility if requested
      if (filters.acceptsAllItems && selectedItems.length > 0) {
        partners = partners.filter((partner: CharityPartner) => {
          return selectedItems.every(item => {
            const acceptsCategory = partner.acceptedItemTypes.includes(item.category);
            const acceptsCondition = partner.requirements.condition.includes(item.condition);
            return acceptsCategory && acceptsCondition;
          });
        });
      }

      setCharityPartners(partners);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      'Children & Youth': 'bg-blue-100 text-blue-800',
      'Women Empowerment': 'bg-purple-100 text-purple-800',
      'Education': 'bg-green-100 text-green-800',
      'Healthcare': 'bg-red-100 text-red-800',
      'Environment': 'bg-emerald-100 text-emerald-800',
      'Community Development': 'bg-orange-100 text-orange-800',
      'Disaster Relief': 'bg-yellow-100 text-yellow-800',
      'Elderly Care': 'bg-indigo-100 text-indigo-800',
      'Disability Support': 'bg-pink-100 text-pink-800',
      'General Welfare': 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const checkItemCompatibility = (partner: CharityPartner) => {
    if (selectedItems.length === 0) return { compatible: true, issues: [] };

    const issues: string[] = [];
    let compatibleItems = 0;

    selectedItems.forEach(item => {
      const acceptsCategory = partner.acceptedItemTypes.includes(item.category);
      const acceptsCondition = partner.requirements.condition.includes(item.condition);

      if (acceptsCategory && acceptsCondition) {
        compatibleItems++;
      } else {
        if (!acceptsCategory) {
          issues.push(`Doesn't accept ${item.category} items`);
        }
        if (!acceptsCondition) {
          issues.push(`Doesn't accept items in ${item.condition} condition`);
        }
      }
    });

    return {
      compatible: compatibleItems === selectedItems.length,
      compatibleCount: compatibleItems,
      totalCount: selectedItems.length,
      issues: [...new Set(issues)] // Remove duplicates
    };
  };

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Choose a Charity Partner</h3>
          <p className="text-gray-600">Select a verified charity organization to receive your donation</p>
        </div>

        {/* Selected Items Summary */}
        {selectedItems.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Items to Donate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {selectedItems.map((item, index) => (
                  <Badge key={item._id} variant="outline" className="text-xs">
                    {item.title} ({item.category}, {item.condition})
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Filter className="h-4 w-4" />
              Filter Charity Partners
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <Input
                placeholder="Search charity partners..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />

              <select
                value={filters.category}
                onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Categories</option>
                {CATEGORIES.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <select
                value={filters.county}
                onChange={(e) => setFilters(prev => ({ ...prev, county: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Counties</option>
                {KENYAN_COUNTIES.map(county => (
                  <option key={county} value={county}>{county}</option>
                ))}
              </select>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="acceptsAllItems"
                checked={filters.acceptsAllItems}
                onChange={(e) => setFilters(prev => ({ ...prev, acceptsAllItems: e.target.checked }))}
                className="h-4 w-4"
              />
              <label htmlFor="acceptsAllItems" className="text-sm text-gray-700">
                Only show partners that accept all my selected items
              </label>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Charity Partners List */}
        <div>
          {isLoading ? (
            <Card>
              <CardContent className="flex items-center justify-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading charity partners...</span>
              </CardContent>
            </Card>
          ) : charityPartners.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-2">No charity partners found</p>
                <p className="text-sm text-gray-400">
                  Try adjusting your search criteria or filters
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {charityPartners.map((partner) => {
                const isSelected = selectedCharity?._id === partner._id;
                const compatibility = checkItemCompatibility(partner);

                return (
                  <Card 
                    key={partner._id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                      isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                    } ${!compatibility.compatible ? 'opacity-75' : ''}`}
                    onClick={() => onCharitySelected(partner)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="text-lg font-semibold">{partner.name}</h4>
                            {getVerificationIcon('verified')}
                            {isSelected && (
                              <Badge className="bg-blue-100 text-blue-800">Selected</Badge>
                            )}
                          </div>
                          
                          <div className="flex flex-wrap gap-2 mb-3">
                            <Badge className={getCategoryColor(partner.category)}>
                              {partner.category}
                            </Badge>
                            {partner.settings.autoAcceptDonations && (
                              <Badge variant="outline" className="text-green-700 border-green-300">
                                Auto-Accept
                              </Badge>
                            )}
                            {partner.settings.providesPickup && (
                              <Badge variant="outline" className="text-blue-700 border-blue-300">
                                Pickup Available
                              </Badge>
                            )}
                          </div>

                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {partner.description}
                          </p>

                          {/* Location */}
                          <div className="flex items-center gap-2 text-sm text-gray-600 mb-3">
                            <MapPin className="h-4 w-4" />
                            <span>{partner.contactInfo.address.town}, {partner.contactInfo.address.county}</span>
                          </div>

                          {/* Stats */}
                          <div className="grid grid-cols-3 gap-4 mb-3">
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
                                <Package className="h-4 w-4" />
                                <span className="font-semibold">{partner.stats.totalDonationsReceived}</span>
                              </div>
                              <p className="text-xs text-gray-500">Donations</p>
                            </div>
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
                                <Users className="h-4 w-4" />
                                <span className="font-semibold">{partner.stats.totalBeneficiaries}</span>
                              </div>
                              <p className="text-xs text-gray-500">Beneficiaries</p>
                            </div>
                            <div className="text-center">
                              <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
                                <Star className="h-4 w-4" />
                                <span className="font-semibold">
                                  {partner.stats.averageRating > 0 
                                    ? partner.stats.averageRating.toFixed(1) 
                                    : 'N/A'
                                  }
                                </span>
                              </div>
                              <p className="text-xs text-gray-500">Rating</p>
                            </div>
                          </div>

                          {/* Item Compatibility */}
                          {selectedItems.length > 0 && (
                            <div className="mb-3">
                              {compatibility.compatible ? (
                                <Alert className="border-green-200 bg-green-50">
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                  <AlertDescription className="text-green-800">
                                    Accepts all {compatibility.totalCount} of your selected items
                                  </AlertDescription>
                                </Alert>
                              ) : (
                                <Alert variant="destructive">
                                  <AlertTriangle className="h-4 w-4" />
                                  <AlertDescription>
                                    Accepts {compatibility.compatibleCount} of {compatibility.totalCount} items. 
                                    {compatibility.issues.slice(0, 2).map((issue, index) => (
                                      <span key={index}> {issue}.</span>
                                    ))}
                                  </AlertDescription>
                                </Alert>
                              )}
                            </div>
                          )}

                          {/* Contact Info */}
                          <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <span>{partner.contactInfo.phone}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              <span className="truncate">{partner.contactInfo.email}</span>
                            </div>
                          </div>
                        </div>

                        {partner.logo && (
                          <div className="ml-4">
                            <img
                              src={partner.logo}
                              alt={`${partner.name} logo`}
                              className="w-16 h-16 rounded-lg object-cover"
                            />
                          </div>
                        )}
                      </div>

                      <div className="flex justify-end">
                        <Button
                          variant={isSelected ? "default" : "outline"}
                          onClick={(e) => {
                            e.stopPropagation();
                            onCharitySelected(partner);
                          }}
                        >
                          {isSelected ? (
                            <>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Selected
                            </>
                          ) : (
                            <>
                              <Heart className="h-4 w-4 mr-2" />
                              Select Partner
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
