import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Truck, 
  MapPin, 
  Clock, 
  Phone, 
  CheckCircle, 
  AlertCircle,
  Package,
  Home,
  Building,
  Info
} from 'lucide-react';
import { CharityPartner, DonationData } from './DonationForm';

interface DeliveryMethodSelectorProps {
  charityPartner: CharityPartner | null;
  deliveryData: Partial<DonationData>;
  onDeliveryConfigured: (deliveryConfig: Partial<DonationData>) => void;
  className?: string;
}

interface DeliveryOption {
  id: 'pickup' | 'delivery' | 'drop_off';
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  available: boolean;
  cost?: string;
  timeframe?: string;
  requirements?: string[];
}

const KENYAN_COUNTIES = [
  'Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Malindi',
  'Kitale', 'Garissa', 'Kakamega', 'Machakos', 'Meru', 'Nyeri', 'Kericho',
  'Embu', 'Nyahururu', 'Nanyuki', 'Voi', 'Kilifi', 'Lamu'
];

export const DeliveryMethodSelector: React.FC<DeliveryMethodSelectorProps> = ({
  charityPartner,
  deliveryData,
  onDeliveryConfigured,
  className = ''
}) => {
  const [selectedMethod, setSelectedMethod] = useState<'pickup' | 'delivery' | 'drop_off'>(
    deliveryData.deliveryMethod || 'pickup'
  );
  const [deliveryAddress, setDeliveryAddress] = useState(deliveryData.deliveryAddress || {
    county: '',
    town: '',
    specificLocation: '',
    contactPhone: ''
  });
  const [dropOffLocation, setDropOffLocation] = useState(deliveryData.dropOffLocation || {
    name: '',
    address: '',
    operatingHours: ''
  });
  const [message, setMessage] = useState(deliveryData.message || '');
  const [isAnonymous, setIsAnonymous] = useState(deliveryData.isAnonymous || false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    validateAndUpdate();
  }, [selectedMethod, deliveryAddress, dropOffLocation, message, isAnonymous]);

  const getDeliveryOptions = (): DeliveryOption[] => {
    if (!charityPartner) return [];

    return [
      {
        id: 'pickup',
        title: 'Charity Pickup',
        description: 'The charity partner will collect items from your location',
        icon: Truck,
        available: charityPartner.settings.providesPickup,
        cost: 'Free',
        timeframe: '2-5 business days',
        requirements: ['Valid contact phone number', 'Accessible pickup location']
      },
      {
        id: 'delivery',
        title: 'Personal Delivery',
        description: 'You will deliver the items to the charity partner',
        icon: Home,
        available: true,
        cost: 'Your transport cost',
        timeframe: 'At your convenience',
        requirements: ['Valid delivery address', 'Coordination with charity']
      },
      {
        id: 'drop_off',
        title: 'Drop-off Point',
        description: 'Drop items at a designated collection point',
        icon: Building,
        available: true,
        cost: 'Free',
        timeframe: 'During operating hours',
        requirements: ['Check operating hours', 'Bring donation reference']
      }
    ];
  };

  const validateAndUpdate = () => {
    const newErrors: { [key: string]: string } = {};

    if (selectedMethod === 'delivery') {
      if (!deliveryAddress.county) newErrors.county = 'County is required';
      if (!deliveryAddress.town) newErrors.town = 'Town is required';
      if (!deliveryAddress.specificLocation) newErrors.specificLocation = 'Specific location is required';
      if (!deliveryAddress.contactPhone) {
        newErrors.contactPhone = 'Contact phone is required';
      } else if (!/^(\+254|0)[17]\d{8}$/.test(deliveryAddress.contactPhone)) {
        newErrors.contactPhone = 'Please enter a valid Kenyan phone number';
      }
    }

    if (selectedMethod === 'drop_off') {
      if (!dropOffLocation.name) newErrors.dropOffName = 'Drop-off location name is required';
      if (!dropOffLocation.address) newErrors.dropOffAddress = 'Drop-off address is required';
      if (!dropOffLocation.operatingHours) newErrors.operatingHours = 'Operating hours are required';
    }

    setErrors(newErrors);

    // Update parent component
    const deliveryConfig: Partial<DonationData> = {
      deliveryMethod: selectedMethod,
      message,
      isAnonymous
    };

    if (selectedMethod === 'delivery' && Object.keys(newErrors).length === 0) {
      deliveryConfig.deliveryAddress = deliveryAddress;
    }

    if (selectedMethod === 'drop_off' && Object.keys(newErrors).length === 0) {
      deliveryConfig.dropOffLocation = dropOffLocation;
    }

    onDeliveryConfigured(deliveryConfig);
  };

  const handleMethodSelect = (method: 'pickup' | 'delivery' | 'drop_off') => {
    setSelectedMethod(method);
  };

  const handleAddressChange = (field: string, value: string) => {
    setDeliveryAddress(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDropOffChange = (field: string, value: string) => {
    setDropOffLocation(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const deliveryOptions = getDeliveryOptions();

  if (!charityPartner) {
    return (
      <div className={className}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please select a charity partner first to configure delivery options.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h3 className="text-lg font-semibold mb-2">Choose Delivery Method</h3>
          <p className="text-gray-600">
            Select how you'd like to get your donation to {charityPartner.name}
          </p>
        </div>

        {/* Charity Partner Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Delivery to: {charityPartner.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-2 text-sm text-gray-600">
              <MapPin className="h-4 w-4 mt-0.5" />
              <div>
                <p>{charityPartner.contactInfo.address.specificLocation}</p>
                <p>{charityPartner.contactInfo.address.town}, {charityPartner.contactInfo.address.county}</p>
                <p className="mt-1">
                  <Phone className="h-3 w-3 inline mr-1" />
                  {charityPartner.contactInfo.phone}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delivery Options */}
        <div className="grid gap-4">
          <h4 className="font-medium">Available Delivery Methods</h4>
          
          {deliveryOptions.map((option) => {
            const Icon = option.icon;
            const isSelected = selectedMethod === option.id;
            
            return (
              <Card 
                key={option.id}
                className={`cursor-pointer transition-all duration-200 ${
                  !option.available ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'
                } ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}
                onClick={() => option.available && handleMethodSelect(option.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-lg ${
                      isSelected ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <Icon className={`h-6 w-6 ${
                        isSelected ? 'text-blue-600' : 'text-gray-600'
                      }`} />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h5 className="font-medium">{option.title}</h5>
                        {!option.available && (
                          <Badge variant="secondary" className="text-xs">
                            Not Available
                          </Badge>
                        )}
                        {isSelected && (
                          <CheckCircle className="h-4 w-4 text-blue-600" />
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{option.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <span className="font-medium text-gray-700">Cost: </span>
                          <span className="text-gray-600">{option.cost}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Timeframe: </span>
                          <span className="text-gray-600">{option.timeframe}</span>
                        </div>
                      </div>
                      
                      {option.requirements && (
                        <div className="mt-2">
                          <p className="text-xs font-medium text-gray-700 mb-1">Requirements:</p>
                          <ul className="text-xs text-gray-600 space-y-0.5">
                            {option.requirements.map((req, index) => (
                              <li key={index}>• {req}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Method-specific Configuration */}
        {selectedMethod === 'delivery' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Delivery Address</CardTitle>
              <CardDescription>
                Provide the address where you'll deliver the items
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">County *</label>
                  <select
                    value={deliveryAddress.county}
                    onChange={(e) => handleAddressChange('county', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.county ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select County</option>
                    {KENYAN_COUNTIES.map(county => (
                      <option key={county} value={county}>{county}</option>
                    ))}
                  </select>
                  {errors.county && (
                    <p className="text-red-500 text-xs mt-1">{errors.county}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Town *</label>
                  <Input
                    value={deliveryAddress.town}
                    onChange={(e) => handleAddressChange('town', e.target.value)}
                    placeholder="Enter town"
                    className={errors.town ? 'border-red-500' : ''}
                  />
                  {errors.town && (
                    <p className="text-red-500 text-xs mt-1">{errors.town}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Specific Location *</label>
                <Textarea
                  value={deliveryAddress.specificLocation}
                  onChange={(e) => handleAddressChange('specificLocation', e.target.value)}
                  placeholder="Provide detailed address (building name, street, landmarks)"
                  rows={3}
                  className={errors.specificLocation ? 'border-red-500' : ''}
                />
                {errors.specificLocation && (
                  <p className="text-red-500 text-xs mt-1">{errors.specificLocation}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Contact Phone *</label>
                <Input
                  value={deliveryAddress.contactPhone}
                  onChange={(e) => handleAddressChange('contactPhone', e.target.value)}
                  placeholder="+254 or 0 followed by phone number"
                  className={errors.contactPhone ? 'border-red-500' : ''}
                />
                {errors.contactPhone && (
                  <p className="text-red-500 text-xs mt-1">{errors.contactPhone}</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {selectedMethod === 'drop_off' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Drop-off Location</CardTitle>
              <CardDescription>
                Configure the drop-off point details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Location Name *</label>
                <Input
                  value={dropOffLocation.name}
                  onChange={(e) => handleDropOffChange('name', e.target.value)}
                  placeholder="e.g., Community Center, Church, School"
                  className={errors.dropOffName ? 'border-red-500' : ''}
                />
                {errors.dropOffName && (
                  <p className="text-red-500 text-xs mt-1">{errors.dropOffName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Address *</label>
                <Textarea
                  value={dropOffLocation.address}
                  onChange={(e) => handleDropOffChange('address', e.target.value)}
                  placeholder="Full address of the drop-off location"
                  rows={2}
                  className={errors.dropOffAddress ? 'border-red-500' : ''}
                />
                {errors.dropOffAddress && (
                  <p className="text-red-500 text-xs mt-1">{errors.dropOffAddress}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Operating Hours *</label>
                <Input
                  value={dropOffLocation.operatingHours}
                  onChange={(e) => handleDropOffChange('operatingHours', e.target.value)}
                  placeholder="e.g., Mon-Fri 9AM-5PM, Weekends 10AM-2PM"
                  className={errors.operatingHours ? 'border-red-500' : ''}
                />
                {errors.operatingHours && (
                  <p className="text-red-500 text-xs mt-1">{errors.operatingHours}</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Additional Options */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Additional Options</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Message to Charity (Optional)</label>
              <Textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Add a personal message or special instructions"
                rows={3}
                maxLength={500}
              />
              <p className="text-xs text-gray-500 mt-1">{message.length}/500 characters</p>
            </div>

            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="anonymous"
                checked={isAnonymous}
                onChange={(e) => setIsAnonymous(e.target.checked)}
                className="h-4 w-4"
              />
              <label htmlFor="anonymous" className="text-sm text-gray-700">
                Make this donation anonymous
              </label>
            </div>
            {isAnonymous && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-sm">
                  Your name will not be shared with the charity partner, but you'll still receive 
                  token rewards and donation tracking information.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
